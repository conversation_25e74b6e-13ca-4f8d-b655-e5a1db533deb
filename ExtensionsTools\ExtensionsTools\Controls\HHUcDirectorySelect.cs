using System;
using System.IO;
using System.Windows.Forms;

namespace ET.Controls
{
    /// <summary>
    /// 目录选择用户控件
    /// </summary>
    /// <remarks>
    /// 此用户控件提供了一个友好的目录选择界面，包括：
    /// 1. 文本框显示当前选择的路径
    /// 2. 选择按钮打开文件夹浏览对话框
    /// 3. 路径变更事件通知
    /// 4. 自适应界面布局
    ///
    /// 主要功能：
    /// - 目录路径的显示和编辑
    /// - 文件夹浏览对话框集成
    /// - 路径选择事件处理
    /// - 界面自适应调整
    /// - 默认路径管理
    ///
    /// 使用场景：
    /// - 文件保存路径选择
    /// - 工作目录设置
    /// - 配置文件路径选择
    /// - 数据导入导出路径设置
    ///
    /// 界面特性：
    /// - 分割容器布局
    /// - 自适应尺寸调整
    /// - 简洁的用户界面
    /// - 直观的操作体验
    /// </remarks>
    public partial class HHUcDirectorySelect : UserControl
    {
        /// <summary>
        /// 路径选择事件处理委托
        /// </summary>
        /// <param name="filePath">选择的文件路径</param>
        public delegate void PathSelectedHandler(string filePath);

        /// <summary>
        /// 默认文件目录
        /// </summary>
        private readonly string _defaultFileDirectory = @"D:\";

        /// <summary>
        /// 获取或设置控件显示的文本（路径）
        /// </summary>
        public override string Text
        {
            get => textBox路径.Text;
            set => textBox路径.Text = value;
        }

        /// <summary>
        /// 路径选择完成事件
        /// </summary>
        public event PathSelectedHandler OnPathSelected;

        /// <summary>
        /// 文本内容变更事件
        /// </summary>
        public new event EventHandler TextChanged;

        /// <summary>
        /// 选择按钮点击事件处理
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        private void Button选择_Click(object sender, EventArgs e)
        {
            PresentFileDialog();
        }

        /// <summary>
        /// 显示文件夹选择对话框
        /// </summary>
        /// <remarks>
        /// 此方法执行以下操作：
        /// 1. 检查当前文本框中的路径是否有效
        /// 2. 设置对话框的初始目录
        /// 3. 显示文件夹浏览对话框
        /// 4. 处理用户选择结果
        /// 5. 触发路径选择事件
        /// </remarks>
        private void PresentFileDialog()
        {
            string initialDirectory = Directory.Exists(textBox路径.Text.Trim()) ? textBox路径.Text.Trim() : _defaultFileDirectory;

            FolderBrowserDialog folderBrowserDialog = new()
            {
                ShowNewFolderButton = true,
                SelectedPath = initialDirectory
            };
            if (folderBrowserDialog.ShowDialog() == DialogResult.OK)
            {
                textBox路径.Text = folderBrowserDialog.SelectedPath;
                OnPathSelected?.Invoke(textBox路径.Text);
            }
        }

        /// <summary>
        /// 路径文本框内容变更事件处理
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        private void TextBox路径_TextChanged(object sender, EventArgs e)
        {
            TextChanged?.Invoke(textBox路径.Text, e);
        }

        #region 初始化及界面控制

        /// <summary>
        /// 构造函数，初始化目录选择控件
        /// </summary>
        public HHUcDirectorySelect()
        {
            InitializeComponent();
            Uc初始化();
        }

        /// <summary>
        /// 控件尺寸变更事件处理
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        private void UcExcelRangeSelect_SizeChanged(object sender, EventArgs e)
        {
            Uc初始化();
        }

        /// <summary>
        /// 用户控件初始化设置
        /// </summary>
        /// <remarks>
        /// 此方法执行以下初始化操作：
        /// 1. 设置控件高度为21像素
        /// 2. 移除边框样式
        /// 3. 设置最小宽度为50像素
        /// 4. 配置分割容器填充整个控件
        /// </remarks>
        private void Uc初始化()
        {
            Height = 21;
            BorderStyle = System.Windows.Forms.BorderStyle.None; // 使用完整命名空间
            Width = Math.Max(50, Width);

            splitContainer1.Dock = DockStyle.Fill;
        }

        /// <summary>
        /// 分割容器尺寸变更事件处理
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        /// <remarks>自动调整分割容器的分割距离，确保右侧按钮保持正方形</remarks>
        private void SplitContainer1_SizeChanged(object sender, EventArgs e)
        {
            splitContainer1.SplitterDistance = splitContainer1.Width - splitContainer1.Panel2.Height + 1;
        }

        #endregion 初始化及界面控制
    }
}